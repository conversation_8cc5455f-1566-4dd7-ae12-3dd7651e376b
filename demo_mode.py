"""
Demo Mode for TeamLogic-AutoTask
Provides mock data and simulated workflow for demonstration purposes.
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List
import random

class DemoDataManager:
    """Manages demo data for the application."""
    
    def __init__(self):
        """Initialize demo data."""
        self.demo_users = self._create_demo_users()
        self.demo_technicians = self._create_demo_technicians()
        self.demo_tickets = self._create_demo_tickets()
    
    def _create_demo_users(self) -> List[Dict]:
        """Create demo users."""
        return [
            {
                'userid': 'USER001',
                'name': '<PERSON>',
                'useremail': '<EMAIL>',
                'userphonenumber': '555-0101'
            },
            {
                'userid': 'USER002',
                'name': '<PERSON>',
                'useremail': '<EMAIL>',
                'userphonenumber': '555-0102'
            },
            {
                'userid': 'USER003',
                'name': '<PERSON>',
                'useremail': '<EMAIL>',
                'userphonenumber': '555-0103'
            },
            {
                'userid': 'USER004',
                'name': '<PERSON>',
                'useremail': '<EMAIL>',
                'userphonenumber': '555-0104'
            },
            {
                'userid': 'USER005',
                'name': 'Demo User',
                'useremail': '<EMAIL>',
                'userphonenumber': '555-0105'
            }
        ]
    
    def _create_demo_technicians(self) -> List[Dict]:
        """Create demo technicians."""
        return [
            {
                'technician_id': 'TECH-001',
                'name': 'Alex Rodriguez',
                'email': '<EMAIL>',
                'roll': 'Senior Technician',
                'skill': 'Hardware, Network, Windows',
                'current_workload': 3,
                'specialization': 'Hardware Troubleshooting'
            },
            {
                'technician_id': 'TECH-002',
                'name': 'Lisa Chen',
                'email': '<EMAIL>',
                'roll': 'Software Specialist',
                'skill': 'Software, Applications, MacOS',
                'current_workload': 2,
                'specialization': 'Software Installation'
            },
            {
                'technician_id': 'TECH-003',
                'name': 'David Thompson',
                'email': '<EMAIL>',
                'roll': 'Network Administrator',
                'skill': 'Network, Security, Linux',
                'current_workload': 4,
                'specialization': 'Network Security'
            },
            {
                'technician_id': 'TECH-004',
                'name': 'Maria Garcia',
                'email': '<EMAIL>',
                'roll': 'Help Desk Technician',
                'skill': 'General Support, Email, Printers',
                'current_workload': 1,
                'specialization': 'User Support'
            }
        ]
    
    def _create_demo_tickets(self) -> List[Dict]:
        """Create demo tickets."""
        tickets = []
        
        # Sample ticket data
        sample_tickets = [
            {
                'title': 'Computer won\'t start',
                'description': 'My Dell laptop won\'t turn on. The power button doesn\'t respond.',
                'priority': 'High',
                'status': 'Assigned',
                'category': 'Hardware',
                'assigned_technician': 'TECH-001',
                'requester_email': '<EMAIL>'
            },
            {
                'title': 'Email not working',
                'description': 'Cannot send or receive emails in Outlook.',
                'priority': 'Medium',
                'status': 'In Progress',
                'category': 'Software',
                'assigned_technician': 'TECH-004',
                'requester_email': '<EMAIL>'
            },
            {
                'title': 'Printer offline',
                'description': 'Network printer shows as offline and won\'t print.',
                'priority': 'Low',
                'status': 'Open',
                'category': 'Hardware',
                'assigned_technician': 'TECH-003',
                'requester_email': '<EMAIL>'
            },
            {
                'title': 'Software installation issue',
                'description': 'Need help installing Adobe Creative Suite on my workstation.',
                'priority': 'Medium',
                'status': 'Resolved',
                'category': 'Software',
                'assigned_technician': 'TECH-002',
                'requester_email': '<EMAIL>',
                'resolution_notes': 'Successfully installed Adobe Creative Suite. User trained on basic features.'
            },
            {
                'title': 'VPN connection problems',
                'description': 'Cannot connect to company VPN from home.',
                'priority': 'Critical',
                'status': 'In Progress',
                'category': 'Network',
                'assigned_technician': 'TECH-003',
                'requester_email': '<EMAIL>'
            }
        ]
        
        # Generate tickets with realistic timestamps
        for i, ticket_data in enumerate(sample_tickets):
            created_time = datetime.now() - timedelta(days=random.randint(0, 7), 
                                                    hours=random.randint(0, 23))
            
            ticket = {
                'ticket_id': f'TKT-DEMO-{str(i+1).zfill(3)}',
                'title': ticket_data['title'],
                'description': ticket_data['description'],
                'priority': ticket_data['priority'],
                'status': ticket_data['status'],
                'category': ticket_data.get('category', 'General'),
                'assigned_technician': ticket_data.get('assigned_technician'),
                'requester_name': self._get_user_name(ticket_data['requester_email']),
                'requester_email': ticket_data['requester_email'],
                'requester_phone': self._get_user_phone(ticket_data['requester_email']),
                'company_id': 'DEMO-COMPANY',
                'device_model': random.choice(['Dell Laptop', 'HP Desktop', 'MacBook Pro', 'Surface Pro']),
                'os_version': random.choice(['Windows 11', 'Windows 10', 'macOS 13', 'Ubuntu 22.04']),
                'location': random.choice(['Building A - Floor 1', 'Building B - Floor 2', 'Remote', 'Building C - Floor 3']),
                'created_at': created_time.isoformat(),
                'updated_at': created_time.isoformat(),
                'due_date': (created_time + timedelta(days=3)).isoformat(),
                'resolution_notes': ticket_data.get('resolution_notes', ''),
                'work_notes': f'[{created_time.isoformat()}] {ticket_data.get("assigned_technician", "SYSTEM")}: Ticket assigned and initial assessment completed.'
            }
            
            tickets.append(ticket)
        
        return tickets
    
    def _get_user_name(self, email: str) -> str:
        """Get user name by email."""
        for user in self.demo_users:
            if user['useremail'] == email:
                return user['name']
        return 'Unknown User'
    
    def _get_user_phone(self, email: str) -> str:
        """Get user phone by email."""
        for user in self.demo_users:
            if user['useremail'] == email:
                return user['userphonenumber']
        return '555-0000'
    
    def add_ticket(self, ticket_data: Dict) -> str:
        """Add a new demo ticket."""
        ticket_id = f'TKT-DEMO-{uuid.uuid4().hex[:6].upper()}'
        current_time = datetime.now().isoformat()
        
        # Simulate AI classification
        categories = ['Hardware', 'Software', 'Network', 'Security', 'General']
        assigned_tech = random.choice(self.demo_technicians)
        
        new_ticket = {
            'ticket_id': ticket_id,
            'title': ticket_data.get('title', ''),
            'description': ticket_data.get('description', ''),
            'priority': ticket_data.get('priority', 'Medium'),
            'status': 'Open',
            'category': random.choice(categories),
            'assigned_technician': assigned_tech['technician_id'],
            'requester_name': ticket_data.get('requester_name', ''),
            'requester_email': ticket_data.get('requester_email', ''),
            'requester_phone': ticket_data.get('requester_phone', ''),
            'company_id': ticket_data.get('company_id', 'DEMO-COMPANY'),
            'device_model': ticket_data.get('device_model', ''),
            'os_version': ticket_data.get('os_version', ''),
            'location': 'Demo Location',
            'created_at': current_time,
            'updated_at': current_time,
            'due_date': ticket_data.get('due_date', ''),
            'resolution_notes': '',
            'work_notes': f'[{current_time}] SYSTEM: Ticket created and automatically classified as {random.choice(categories)}. Assigned to {assigned_tech["name"]}.'
        }
        
        self.demo_tickets.append(new_ticket)
        return ticket_id
    
    def get_tickets(self) -> List[Dict]:
        """Get all demo tickets."""
        return self.demo_tickets
    
    def get_users(self) -> List[Dict]:
        """Get all demo users."""
        return self.demo_users
    
    def get_technicians(self) -> List[Dict]:
        """Get all demo technicians."""
        return self.demo_technicians
    
    def update_ticket(self, ticket_id: str, updates: Dict) -> bool:
        """Update a demo ticket."""
        for ticket in self.demo_tickets:
            if ticket['ticket_id'] == ticket_id:
                ticket.update(updates)
                ticket['updated_at'] = datetime.now().isoformat()
                return True
        return False

# Global demo data manager instance
demo_data = DemoDataManager()
