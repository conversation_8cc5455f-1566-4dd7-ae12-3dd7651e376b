# TeamLogic-AutoTask Setup Guide

## Overview
This application has been updated to remove authentication and connect directly to your Snowflake database with the existing workflow system.

## Key Changes Made

### ✅ Removed Authentication
- No more login system - direct access to User and Technician dashboards
- Role selection via sidebar buttons

### ✅ Connected to Real Snowflake Database
- Uses your existing Snowflake tables:
  - `company_4130`
  - `technician_dummy_data`
  - `tickets`
  - `user_dummy_data`

### ✅ Integrated Workflow System
- Ticket submission triggers your existing workflow
- Automatic classification and technician assignment
- Email notifications sent to users

### ✅ Fixed UI Issues
- Resolved duplicate key errors
- Enhanced user interface
- Better error handling

## Setup Instructions

### 1. Configure Snowflake Credentials

Update `.streamlit/secrets.toml` with your Snowflake credentials:

```toml
[snowflake]
account = "your_account_identifier"
user = "your_username"
password = "your_password"
warehouse = "your_warehouse"
database = "your_database"
schema = "your_schema"
role = "your_role"
passcode = ""
```

### 2. Install Dependencies

```bash
pip install streamlit pandas plotly snowflake-connector-python
```

### 3. Run the Application

```bash
streamlit run app.py
```

## How It Works

### User Workflow
1. **User selects "User Dashboard"** from sidebar
2. **Enters email address** to get started
3. **Submits ticket** with details
4. **Ticket is saved** to Snowflake `tickets` table
5. **Workflow is triggered** automatically:
   - AI classification
   - Technician assignment
   - Email notification
6. **User can track** ticket status in "My Tickets"

### Technician Workflow
1. **Technician selects "Technician Dashboard"** from sidebar
2. **Enters technician ID** (from `technician_dummy_data` table)
3. **Views assigned tickets** from database
4. **Updates ticket status/priority** directly in database
5. **Adds work notes** to tickets
6. **Resolution displayed** to user automatically

### Database Integration
- **Real-time data** from Snowflake
- **Automatic workflow** triggers on ticket submission
- **Email notifications** via existing system
- **Status updates** reflected immediately

## Features

### User Dashboard
- ✅ Submit new tickets
- ✅ View ticket history
- ✅ Track ticket status
- ✅ Real-time updates

### Technician Dashboard
- ✅ View assigned tickets
- ✅ Update ticket status
- ✅ Change priority levels
- ✅ Add work notes
- ✅ Contact users

### System Features
- ✅ AI-powered ticket classification
- ✅ Automatic technician assignment
- ✅ Email notifications
- ✅ Real-time status updates
- ✅ Dark/Light theme support

## Troubleshooting

### Database Connection Issues
1. Verify Snowflake credentials in `secrets.toml`
2. Check network connectivity
3. Ensure database tables exist

### Workflow Issues
1. Check intake agent configuration
2. Verify email settings
3. Check technician availability

### UI Issues
1. Clear browser cache
2. Refresh the page
3. Check console for errors

## Support
For technical support, contact the development team or check the application logs for detailed error messages.
