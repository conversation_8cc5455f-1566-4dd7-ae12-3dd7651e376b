"""
TeamLogic-AutoTask Application
Direct access to User and Technician dashboards with real Snowflake backend integration.
"""

import warnings
warnings.filterwarnings("ignore", message="You have an incompatible version of 'pyarrow' installed")

import streamlit as st
import sys
import os

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import the enhanced UI pages
from src.ui.Pages.User import main as user_main
from src.ui.Pages.Technician import main as technician_main
from src.ui.components import apply_custom_css

def main():
    """Main application entry point with role selection."""

    st.set_page_config(
        page_title="TeamLogic-AutoTask",
        layout="wide",
        page_icon="🎫",
        initial_sidebar_state="expanded"
    )

    # Apply custom CSS
    apply_custom_css()

    # Initialize session state
    if "selected_role" not in st.session_state:
        st.session_state.selected_role = None

    # Role selection in sidebar
    with st.sidebar:
        st.markdown("## 🎯 Select Your Role")

        if st.button("👤 User Dashboard", key="select_user", use_container_width=True):
            st.session_state.selected_role = "user"
            st.rerun()

        if st.button("👨‍🔧 Technician Dashboard", key="select_technician", use_container_width=True):
            st.session_state.selected_role = "technician"
            st.rerun()

        if st.session_state.selected_role:
            st.markdown(f"**Current Role:** {st.session_state.selected_role.title()}")

            if st.button("🔄 Switch Role", key="switch_role", use_container_width=True):
                st.session_state.selected_role = None
                st.rerun()

    # Show appropriate dashboard based on selected role
    if st.session_state.selected_role == "user":
        try:
            user_main()
        except Exception as e:
            st.error(f"❌ Error loading User Dashboard: {str(e)}")
            st.info("🔄 Please refresh the page or switch roles.")

    elif st.session_state.selected_role == "technician":
        try:
            technician_main()
        except Exception as e:
            st.error(f"❌ Error loading Technician Dashboard: {str(e)}")
            st.info("🔄 Please refresh the page or switch roles.")

    else:
        # Show role selection page with improved styling
        st.markdown("""
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="color: #4e73df; font-size: 3rem; margin-bottom: 0.5rem;">🎫 TeamLogic-AutoTask</h1>
            <h3 style="color: #858796; font-weight: 400;">Welcome to the Support Ticket Management System</h3>
        </div>
        """, unsafe_allow_html=True)

        # Dashboard selection cards
        col1, col2 = st.columns(2, gap="large")

        with col1:
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 2rem;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                text-align: center;
                margin: 1rem 0;
                transition: transform 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="font-size: 4rem; margin-bottom: 1rem;">👤</div>
                <h2 style="margin-bottom: 1rem; color: white;">User Dashboard</h2>
                <p style="font-size: 1.1rem; margin-bottom: 1.5rem; opacity: 0.9;">
                    Submit new support tickets and track your existing tickets
                </p>
                <div style="text-align: left; margin: 1rem 0;">
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Submit new tickets</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Track ticket status</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> View ticket history</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Get automated resolutions</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Analytics dashboard</div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            st.markdown("""
            <div style="
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
                padding: 2rem;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                text-align: center;
                margin: 1rem 0;
                transition: transform 0.3s ease;
                cursor: pointer;
            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                <div style="font-size: 4rem; margin-bottom: 1rem;">👨‍🔧</div>
                <h2 style="margin-bottom: 1rem; color: white;">Technician Dashboard</h2>
                <p style="font-size: 1.1rem; margin-bottom: 1.5rem; opacity: 0.9;">
                    Manage assigned tickets and update their status
                </p>
                <div style="text-align: left; margin: 1rem 0;">
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> View assigned tickets</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Update ticket status</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Change priority levels</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Add resolution notes</div>
                    <div style="margin: 0.5rem 0;"><span style="color: #90EE90;">✓</span> Performance analytics</div>
                </div>
            </div>
            """, unsafe_allow_html=True)

        # Instructions
        st.markdown("""
        <div style="
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            text-align: center;
            margin: 2rem 0;
            font-size: 1.1rem;
        ">
            👆 Please select your role from the sidebar to get started
        </div>
        """, unsafe_allow_html=True)

        # System status and features overview
        st.markdown("---")

        # System Status
        st.markdown("### 🔧 System Status")
        status_col1, status_col2, status_col3, status_col4 = st.columns(4)

        with status_col1:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 10px; margin: 0.5rem 0;">
                <div style="font-size: 2rem;">🗄️</div>
                <div><strong>Database</strong></div>
                <div style="color: #155724;">✅ Connected</div>
            </div>
            """, unsafe_allow_html=True)

        with status_col2:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 10px; margin: 0.5rem 0;">
                <div style="font-size: 2rem;">🤖</div>
                <div><strong>AI Classification</strong></div>
                <div style="color: #155724;">✅ Active</div>
            </div>
            """, unsafe_allow_html=True)

        with status_col3:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 10px; margin: 0.5rem 0;">
                <div style="font-size: 2rem;">👨‍🔧</div>
                <div><strong>Assignment</strong></div>
                <div style="color: #155724;">✅ Ready</div>
            </div>
            """, unsafe_allow_html=True)

        with status_col4:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 10px; margin: 0.5rem 0;">
                <div style="font-size: 2rem;">📧</div>
                <div><strong>Notifications</strong></div>
                <div style="color: #155724;">✅ Enabled</div>
            </div>
            """, unsafe_allow_html=True)

        st.markdown("### 🚀 System Features")

        feature_col1, feature_col2, feature_col3 = st.columns(3)

        with feature_col1:
            st.markdown("""
            **🤖 AI-Powered Workflow**
            - Automatic ticket classification using Snowflake Cortex LLM
            - Smart technician assignment based on skills & workload
            - AI-generated resolution suggestions
            - Metadata extraction from descriptions
            """)

        with feature_col2:
            st.markdown("""
            **📊 Real-time Analytics**
            - Performance metrics and KPIs
            - Ticket trends and patterns
            - Resolution time tracking
            - Technician workload monitoring
            """)

        with feature_col3:
            st.markdown("""
            **🔔 Automated Notifications**
            - Email alerts for ticket updates
            - Assignment notifications to technicians
            - Resolution confirmations to users
            - Escalation alerts for urgent tickets
            """)

        # Workflow diagram
        st.markdown("---")
        st.markdown("### 🔄 Ticket Processing Workflow")

        st.markdown("""
        <div style="background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 15px; margin: 1rem 0;">
            <div style="text-align: center; font-size: 1.2rem; margin-bottom: 1rem;">
                <strong>End-to-End Automated Workflow</strong>
            </div>
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <div style="text-align: center; margin: 0.5rem;">
                    <div style="font-size: 2rem;">📝</div>
                    <div>Ticket Submission</div>
                </div>
                <div style="font-size: 1.5rem;">→</div>
                <div style="text-align: center; margin: 0.5rem;">
                    <div style="font-size: 2rem;">🤖</div>
                    <div>AI Classification</div>
                </div>
                <div style="font-size: 1.5rem;">→</div>
                <div style="text-align: center; margin: 0.5rem;">
                    <div style="font-size: 2rem;">👨‍🔧</div>
                    <div>Auto Assignment</div>
                </div>
                <div style="font-size: 1.5rem;">→</div>
                <div style="text-align: center; margin: 0.5rem;">
                    <div style="font-size: 2rem;">📧</div>
                    <div>Notifications</div>
                </div>
                <div style="font-size: 1.5rem;">→</div>
                <div style="text-align: center; margin: 0.5rem;">
                    <div style="font-size: 2rem;">✅</div>
                    <div>Resolution</div>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
