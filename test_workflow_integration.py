"""
Comprehensive test script for TeamLogic-AutoTask workflow integration.
Tests the complete end-to-end workflow from ticket submission to resolution.
"""

import sys
import os
import time
from datetime import datetime

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.services.database_service import DatabaseService
from src.agents.intake_agent import IntakeClassificationAgent

def test_database_connection():
    """Test database connection and basic operations."""
    print("\n🔍 Testing Database Connection...")
    
    try:
        db_service = DatabaseService()
        
        if db_service.db_connection:
            print("✅ Database connection established")
            
            # Test getting users
            users = db_service.get_users()
            print(f"✅ Retrieved {len(users)} users from database")
            
            # Test getting technicians
            technicians = db_service.get_technicians()
            print(f"✅ Retrieved {len(technicians)} technicians from database")
            
            # Test getting tickets
            tickets = db_service.get_tickets()
            print(f"✅ Retrieved {len(tickets)} tickets from database")
            
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False

def test_ai_classification():
    """Test AI classification service."""
    print("\n🤖 Testing AI Classification Service...")
    
    try:
        db_service = DatabaseService()
        
        if db_service.intake_agent:
            print("✅ AI Classification agent initialized")
            
            # Test metadata extraction
            test_title = "Computer won't start"
            test_description = "My Dell laptop won't turn on. The power button doesn't respond and there are no lights."
            
            metadata = db_service.intake_agent.extract_metadata(test_title, test_description)
            if metadata:
                print("✅ Metadata extraction working")
                print(f"   Extracted: {metadata}")
            else:
                print("⚠️ Metadata extraction returned no results")
            
            return True
        else:
            print("❌ AI Classification agent not available")
            return False
            
    except Exception as e:
        print(f"❌ AI Classification test failed: {str(e)}")
        return False

def test_ticket_creation_workflow():
    """Test complete ticket creation workflow."""
    print("\n🎫 Testing Ticket Creation Workflow...")
    
    try:
        db_service = DatabaseService()
        
        # Create test ticket data
        test_ticket = {
            'title': 'Test Workflow Ticket',
            'description': 'This is a test ticket to verify the complete workflow integration including AI classification, assignment, and notifications.',
            'priority': 'Medium',
            'requester_name': 'Test User',
            'requester_email': '<EMAIL>',
            'requester_phone': '555-0123',
            'company_id': 'TEST001',
            'device_model': 'Dell Laptop',
            'os_version': 'Windows 11',
            'error_message': 'No specific error message',
            'due_date': datetime.now().isoformat()
        }
        
        print("📝 Creating test ticket...")
        ticket_id = db_service.create_ticket(test_ticket)
        
        if ticket_id:
            print(f"✅ Ticket created successfully: {ticket_id}")
            
            # Wait a moment for workflow processing
            print("⏳ Waiting for workflow processing...")
            time.sleep(2)
            
            # Verify ticket was saved
            tickets = db_service.get_tickets()
            created_ticket = next((t for t in tickets if t.get('ticket_id') == ticket_id), None)
            
            if created_ticket:
                print("✅ Ticket saved to database")
                print(f"   Status: {created_ticket.get('status', 'Unknown')}")
                print(f"   Priority: {created_ticket.get('priority', 'Unknown')}")
                
                # Check if workflow processing occurred
                if created_ticket.get('category'):
                    print(f"✅ AI Classification completed: {created_ticket.get('category')}")
                else:
                    print("⚠️ AI Classification may not have completed")
                
                if created_ticket.get('assigned_technician'):
                    print(f"✅ Technician assignment completed: {created_ticket.get('assigned_technician')}")
                else:
                    print("⚠️ Technician assignment may not have completed")
                
                return True
            else:
                print("❌ Ticket not found in database after creation")
                return False
        else:
            print("❌ Ticket creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Ticket creation workflow test failed: {str(e)}")
        return False

def test_technician_assignment():
    """Test technician assignment functionality."""
    print("\n👨‍🔧 Testing Technician Assignment...")
    
    try:
        db_service = DatabaseService()
        
        if db_service.intake_agent and db_service.intake_agent.assignment_agent:
            print("✅ Assignment agent available")
            
            # Get available technicians
            technicians = db_service.get_technicians()
            if technicians:
                print(f"✅ Found {len(technicians)} available technicians")
                for tech in technicians[:3]:  # Show first 3
                    print(f"   - {tech.get('name', 'Unknown')} ({tech.get('technician_id', 'N/A')})")
                return True
            else:
                print("⚠️ No technicians found in database")
                return False
        else:
            print("❌ Assignment agent not available")
            return False
            
    except Exception as e:
        print(f"❌ Technician assignment test failed: {str(e)}")
        return False

def test_notification_system():
    """Test notification system."""
    print("\n📧 Testing Notification System...")
    
    try:
        db_service = DatabaseService()
        
        if db_service.intake_agent and db_service.intake_agent.notification_agent:
            print("✅ Notification agent available")
            print("✅ Email notification system ready")
            return True
        else:
            print("❌ Notification agent not available")
            return False
            
    except Exception as e:
        print(f"❌ Notification system test failed: {str(e)}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary."""
    print("🚀 Starting Comprehensive Workflow Integration Test")
    print("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("AI Classification", test_ai_classification),
        ("Ticket Creation Workflow", test_ticket_creation_workflow),
        ("Technician Assignment", test_technician_assignment),
        ("Notification System", test_notification_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The workflow integration is working correctly.")
        print("\n✅ System Status:")
        print("   - Database connectivity: ✅ Working")
        print("   - AI Classification: ✅ Working")
        print("   - Ticket Creation: ✅ Working")
        print("   - Technician Assignment: ✅ Working")
        print("   - Notification System: ✅ Working")
        print("\n🚀 The system is ready for production use!")
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
        print("\n📋 Troubleshooting:")
        print("   1. Check Snowflake credentials in .env file")
        print("   2. Verify database tables exist and have data")
        print("   3. Ensure all required dependencies are installed")
        print("   4. Check network connectivity to Snowflake")
    
    return passed == total

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    print("\n📋 Next Steps:")
    print("1. Open the Streamlit app: http://localhost:8501")
    print("2. Test User Dashboard: Submit a ticket and track its progress")
    print("3. Test Technician Dashboard: View assigned tickets and update status")
    print("4. Verify email notifications are sent")
    
    sys.exit(0 if success else 1)
