"""
User Dashboard for TeamLogic-AutoTask Application
Comprehensive user interface for ticket submission, tracking, and analytics.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import sys
import os

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.ui.components import (
    apply_custom_css, create_metric_card, create_status_badge, 
    get_priority_icon, format_time_elapsed, format_date_display,
    create_chart_container, create_filter_section, create_sidebar
)
from src.ui.login import show_login_page, require_authentication, logout
from src.services.database_service import DatabaseService
from config import PRIORITY_OPTIONS, STATUS_OPTIONS, DURATION_OPTIONS

class UserDashboard:
    """Main User Dashboard class."""
    
    def __init__(self):
        """Initialize the user dashboard."""
        self.db_service = DatabaseService()
        
        # Initialize session state
        if "current_page" not in st.session_state:
            st.session_state.current_page = "home"
        if "dark_theme" not in st.session_state:
            st.session_state.dark_theme = False
    
    def show_header(self, page_title: str, user_info: Dict):
        """Show the page header with user information."""
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.title(f"🎫 {page_title}")
            st.markdown(f"**Welcome, {user_info['name']}!**")
        
        with col2:
            st.markdown("### Quick Stats")
            # Get user's ticket stats
            tickets = self.get_user_tickets(user_info['email'])
            total_tickets = len(tickets)
            open_tickets = len([t for t in tickets if t.get('status') in ['Open', 'In Progress']])
            st.metric("Total Tickets", total_tickets)
            st.metric("Open Tickets", open_tickets)
        
        with col3:
            st.markdown("### Actions")
            if st.button("🔄 Refresh", key="header_refresh"):
                st.rerun()
            if st.button("🚪 Logout", key="header_logout"):
                logout()
    
    def show_ticket_submission_form(self, user_info: Dict):
        """Show the ticket submission form."""
        st.subheader("📝 Submit New Support Ticket")
        
        with st.form("ticket_submission_form", clear_on_submit=True):
            col1, col2 = st.columns(2)
            
            with col1:
                title = st.text_input(
                    "Ticket Title *",
                    placeholder="Brief description of the issue",
                    help="Provide a clear, concise title for your issue"
                )
                
                priority = st.selectbox(
                    "Priority *",
                    options=PRIORITY_OPTIONS,
                    index=1,  # Default to Medium
                    help="Select the urgency level of your issue"
                )
                
                device_model = st.text_input(
                    "Device Model",
                    placeholder="e.g., Dell Laptop, iPhone 12",
                    help="What device are you having issues with?"
                )
                
                due_date = st.date_input(
                    "Preferred Resolution Date",
                    value=datetime.now() + timedelta(days=3),
                    min_value=datetime.now(),
                    help="When do you need this resolved?"
                )
            
            with col2:
                description = st.text_area(
                    "Detailed Description *",
                    placeholder="Please describe the issue in detail...",
                    height=100,
                    help="Provide as much detail as possible to help us resolve your issue quickly"
                )
                
                os_version = st.text_input(
                    "Operating System",
                    placeholder="e.g., Windows 11, macOS 13.0",
                    help="What operating system are you using?"
                )
                
                error_message = st.text_area(
                    "Error Messages",
                    placeholder="Copy any error messages you see here...",
                    height=80,
                    help="Include any error codes or messages"
                )
            
            # Submit button
            submitted = st.form_submit_button("🎫 Submit Ticket", use_container_width=True)
            
            if submitted:
                if not title or not description:
                    st.error("Please fill in all required fields (marked with *)")
                else:
                    # Create ticket data
                    ticket_data = {
                        'title': title,
                        'description': description,
                        'priority': priority,
                        'requester_name': user_info['name'],
                        'requester_email': user_info['email'],
                        'requester_phone': user_info.get('phone', ''),
                        'company_id': user_info.get('user_id', ''),
                        'device_model': device_model,
                        'os_version': os_version,
                        'error_message': error_message,
                        'due_date': due_date.isoformat()
                    }
                    
                    try:
                        # Submit ticket
                        ticket_id = self.db_service.create_ticket(ticket_data)
                        st.success(f"✅ Ticket submitted successfully! Ticket ID: {ticket_id}")
                        st.info("You will receive an email confirmation shortly.")
                        
                        # Show ticket details
                        with st.expander("📋 Ticket Details", expanded=True):
                            col1, col2 = st.columns(2)
                            with col1:
                                st.write(f"**Ticket ID:** {ticket_id}")
                                st.write(f"**Title:** {title}")
                                st.write(f"**Priority:** {get_priority_icon(priority)} {priority}")
                            with col2:
                                st.write(f"**Status:** Open")
                                st.write(f"**Created:** {datetime.now().strftime('%Y-%m-%d %H:%M')}")
                                st.write(f"**Due Date:** {due_date.strftime('%Y-%m-%d')}")
                        
                    except Exception as e:
                        st.error(f"❌ Error submitting ticket: {str(e)}")
    
    def get_user_tickets(self, user_email: str) -> List[Dict]:
        """Get tickets for the current user."""
        try:
            all_tickets = self.db_service.get_tickets()
            user_tickets = [
                ticket for ticket in all_tickets 
                if ticket.get('requester_email', '').lower() == user_email.lower()
            ]
            return user_tickets
        except Exception as e:
            st.error(f"Error fetching tickets: {str(e)}")
            return []
    
    def show_recent_tickets_preview(self, user_info: Dict):
        """Show a preview of recent tickets."""
        st.subheader("🕒 Recent Tickets")
        
        tickets = self.get_user_tickets(user_info['email'])
        
        if not tickets:
            st.info("No tickets found. Submit your first ticket above!")
            return
        
        # Sort by creation date (most recent first)
        tickets.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        recent_tickets = tickets[:5]  # Show last 5 tickets
        
        for ticket in recent_tickets:
            with st.expander(
                f"{get_priority_icon(ticket.get('priority', 'Medium'))} "
                f"{ticket.get('title', 'Untitled')} - "
                f"{ticket.get('status', 'Unknown')}"
            ):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**ID:** {ticket.get('ticket_id', 'N/A')}")
                    st.write(f"**Status:** {create_status_badge(ticket.get('status', 'Unknown'))}", 
                            unsafe_allow_html=True)
                    st.write(f"**Priority:** {get_priority_icon(ticket.get('priority', 'Medium'))} "
                            f"{ticket.get('priority', 'Medium')}")
                
                with col2:
                    st.write(f"**Created:** {format_date_display(ticket.get('created_at', ''))}")
                    st.write(f"**Time Elapsed:** {format_time_elapsed(ticket.get('created_at', ''))}")
                    if ticket.get('assigned_technician'):
                        st.write(f"**Assigned to:** {ticket.get('assigned_technician', 'Unassigned')}")
                
                if ticket.get('description'):
                    st.write(f"**Description:** {ticket.get('description', '')[:200]}...")
                
                if ticket.get('resolution_notes'):
                    st.success(f"**Resolution:** {ticket.get('resolution_notes', '')}")
        
        if len(tickets) > 5:
            st.info(f"Showing 5 of {len(tickets)} tickets. View all in 'My Tickets' page.")
    
    def show_main_page(self, user_info: Dict):
        """Show the main dashboard page."""
        self.show_header("User Dashboard", user_info)
        
        st.markdown("---")
        
        # Two-column layout
        col1, col2 = st.columns([1, 1])
        
        with col1:
            self.show_ticket_submission_form(user_info)
        
        with col2:
            self.show_recent_tickets_preview(user_info)
    
    def show_tickets_page(self, user_info: Dict):
        """Show the tickets management page."""
        self.show_header("My Tickets", user_info)
        
        st.markdown("---")
        
        tickets = self.get_user_tickets(user_info['email'])
        
        if not tickets:
            st.info("No tickets found. Submit your first ticket from the Home page!")
            return
        
        # Filters
        filters = {
            "status": list(set([t.get('status', 'Unknown') for t in tickets])),
            "priority": list(set([t.get('priority', 'Medium') for t in tickets])),
            "date_range": True
        }
        
        applied_filters = create_filter_section(filters)
        
        # Filter tickets based on applied filters
        filtered_tickets = tickets
        
        if applied_filters.get('status'):
            filtered_tickets = [t for t in filtered_tickets if t.get('status') in applied_filters['status']]
        
        if applied_filters.get('priority'):
            filtered_tickets = [t for t in filtered_tickets if t.get('priority') in applied_filters['priority']]
        
        # Display tickets
        st.subheader(f"📋 Tickets ({len(filtered_tickets)} found)")
        
        for ticket in filtered_tickets:
            with st.container():
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                
                with col1:
                    st.write(f"**{ticket.get('title', 'Untitled')}**")
                    st.write(f"ID: {ticket.get('ticket_id', 'N/A')}")
                
                with col2:
                    st.write(create_status_badge(ticket.get('status', 'Unknown')), 
                            unsafe_allow_html=True)
                
                with col3:
                    st.write(f"{get_priority_icon(ticket.get('priority', 'Medium'))} "
                            f"{ticket.get('priority', 'Medium')}")
                
                with col4:
                    st.write(format_time_elapsed(ticket.get('created_at', '')))
                
                # Expandable details
                with st.expander("View Details"):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write(f"**Description:** {ticket.get('description', 'No description')}")
                        if ticket.get('device_model'):
                            st.write(f"**Device:** {ticket.get('device_model')}")
                        if ticket.get('os_version'):
                            st.write(f"**OS:** {ticket.get('os_version')}")
                    
                    with col2:
                        st.write(f"**Created:** {format_date_display(ticket.get('created_at', ''))}")
                        if ticket.get('assigned_technician'):
                            st.write(f"**Assigned to:** {ticket.get('assigned_technician')}")
                        if ticket.get('resolution_notes'):
                            st.success(f"**Resolution:** {ticket.get('resolution_notes')}")
                
                st.markdown("---")

    def show_analytics_page(self, user_info: Dict):
        """Show the analytics dashboard page."""
        self.show_header("Analytics Dashboard", user_info)

        st.markdown("---")

        tickets = self.get_user_tickets(user_info['email'])

        if not tickets:
            st.info("No tickets found for analytics. Submit some tickets first!")
            return

        # Metrics row
        col1, col2, col3, col4 = st.columns(4)

        total_tickets = len(tickets)
        open_tickets = len([t for t in tickets if t.get('status') in ['Open', 'In Progress']])
        resolved_tickets = len([t for t in tickets if t.get('status') == 'Resolved'])
        closed_tickets = len([t for t in tickets if t.get('status') == 'Closed'])

        with col1:
            create_metric_card("Total Tickets", str(total_tickets), card_type="primary")

        with col2:
            create_metric_card("Open Tickets", str(open_tickets), card_type="warning")

        with col3:
            create_metric_card("Resolved", str(resolved_tickets), card_type="success")

        with col4:
            create_metric_card("Closed", str(closed_tickets), card_type="info")

        # Charts row
        col1, col2 = st.columns(2)

        with col1:
            # Status distribution
            status_counts = {}
            for ticket in tickets:
                status = ticket.get('status', 'Unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            if status_counts:
                create_chart_container("pie", status_counts, "Tickets by Status")

        with col2:
            # Priority distribution
            priority_counts = {}
            for ticket in tickets:
                priority = ticket.get('priority', 'Medium')
                priority_counts[priority] = priority_counts.get(priority, 0) + 1

            if priority_counts:
                create_chart_container("bar", priority_counts, "Tickets by Priority")

        # Recent activity
        st.subheader("📈 Recent Activity")

        # Sort tickets by creation date
        sorted_tickets = sorted(tickets, key=lambda x: x.get('created_at', ''), reverse=True)
        recent_tickets = sorted_tickets[:10]

        for ticket in recent_tickets:
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                st.write(f"**{ticket.get('title', 'Untitled')}**")

            with col2:
                st.write(create_status_badge(ticket.get('status', 'Unknown')),
                        unsafe_allow_html=True)

            with col3:
                st.write(format_time_elapsed(ticket.get('created_at', '')))

def main():
    """Main function for the User Dashboard."""
    # Configure page
    st.set_page_config(
        page_title="User Dashboard - TeamLogic-AutoTask",
        layout="wide",
        page_icon="👤",
        initial_sidebar_state="expanded"
    )
    
    # Apply custom CSS
    apply_custom_css()
    
    # Check authentication
    user_info = require_authentication("user")
    
    if not user_info:
        # Show login page
        show_login_page("user")
        return
    
    # Initialize dashboard
    dashboard = UserDashboard()
    
    # Sidebar navigation
    current_page = create_sidebar(st.session_state.current_page, user_info)
    st.session_state.current_page = current_page
    
    # Show appropriate page
    if current_page == "home":
        dashboard.show_main_page(user_info)
    elif current_page == "tickets":
        dashboard.show_tickets_page(user_info)
    elif current_page == "dashboard":
        dashboard.show_analytics_page(user_info)
    else:
        dashboard.show_main_page(user_info)

if __name__ == "__main__":
    main()
