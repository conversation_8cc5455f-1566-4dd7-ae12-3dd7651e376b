TeamLogic AutoTask - IT Support Ticket Management System
1. Overall Goal and Primary Objectives
TeamLogic AutoTask is a comprehensive AI-powered IT support ticket management system built with Streamlit and Snowflake Cortex LLM. The primary objectives are:

Automate the IT support ticket lifecycle from creation to resolution
Leverage AI for intelligent ticket classification and assignment
Provide a streamlined interface for users and technicians
Create a knowledge base of past tickets for improved resolution times
Offer real-time analytics and reporting on support operations
2. Features by Category
User Management
Role-based access (User, Technician, Admin)
User authentication system
User profiles with contact information
Ticket Management
Manual ticket creation via web interface
Automated ticket creation from emails (IMAP integration)
Ticket tracking and status updates
Work notes and resolution documentation
Due date management
AI-Powered Components
Ticket classification using Snowflake Cortex LLM
Metadata extraction from ticket descriptions
Intelligent technician assignment based on skills and workload
AI-generated resolution suggestions
Similar ticket identification
Communication
Email notifications for ticket updates
Automated confirmation emails to users
Technician-to-user communication tools
Knowledge Management
Persistent knowledge base of past tickets
Similar ticket tracking for faster resolution
Resolution templates based on historical data
Analytics & Reporting
Real-time dashboard with ticket metrics
Technician performance tracking
Resolution time analytics
3. Component Functionality Breakdown
Database Layer ( src/database/snowflake_db.py)
Manages connections to Snowflake database
Executes queries against Snowflake tables
Interfaces with Snowflake Cortex LLM for AI operations
Database Service ( src/services/database_service.py)
Higher-level database operations
Ticket CRUD operations
Technician data management
Workflow triggering
AI Processor ( src/processors/ai_processor.py)
Extracts metadata from ticket descriptions using LLM
Classifies tickets by type, priority, and category
Generates resolution suggestions based on ticket content
Uses Snowflake Cortex LLM models (mixtral-8x7b, llama3-8b)
Intake Agent ( src/agents/intake_agent.py)
Orchestrates the ticket processing workflow
Coordinates between different components
Processes new tickets from various sources
Saves ticket data to knowledge base
Assignment Agent ( src/agents/assignment_agent.py)
Analyzes ticket requirements using Cortex LLM
Matches tickets to technicians based on skills
Considers technician workload and availability
Provides fallback assignment when AI fails
Notification Agent (src/agents/notification_agent.py)
Sends email notifications to users
Delivers ticket updates to stakeholders
Manages communication templates
UI Components
User Dashboard (src/ui/Pages/User.py)
Technician Dashboard ( src/ui/Pages/Technician.py)
Login System ( src/ui/login.py)
Shared UI components ( src/ui/components.py)
Backend API ( backend/)
FastAPI implementation for headless operations
RESTful endpoints for ticket operations
Pydantic models for data validation
4. End-to-End Ticket Management Workflow
Ticket Creation
User submits ticket via web interface or email
System generates unique ticket ID (e.g., TKT-[UUID])
Initial ticket data stored in Snowflake database
Metadata Extraction
AI processor extracts structured metadata from description
Identifies key information like device details, error messages
Classification
Cortex LLM classifies ticket by issue type, sub-issue type, category
Determines priority level based on content analysis
Sets initial status to "Open"
Similar Ticket Matching
System searches for similar past tickets
Identifies potential resolution patterns
Technician Assignment
Assignment agent analyzes required skills using LLM
Matches ticket to technician based on skills, specialization
Considers current workload and availability
Updates ticket with assigned technician
Resolution Suggestion
AI generates suggested resolution steps
Provides technician with starting point for troubleshooting
Notification
System sends confirmation email to user
Notifies assigned technician of new ticket
Resolution Process
Technician works on ticket and updates status
Adds work notes documenting progress
Changes priority if needed
Closure
Technician marks ticket as resolved
System updates knowledge base with resolution
Final notification sent to user
Knowledge Base Update
Ticket data and resolution stored for future reference
Similar ticket metadata updated
5. User Journeys by Role
End User Workflow
User logs in with credentials (or uses direct access in newer version)
Navigates to User Dashboard
Submits new ticket with description, device details
Receives confirmation email with ticket number
Tracks ticket status through dashboard
Reviews resolution when complete
Can submit follow-up tickets if needed
Technician Workflow
Technician logs in with credentials
Views assigned tickets on Technician Dashboard
Prioritizes work based on ticket priority
Reviews AI-suggested resolution steps
Updates ticket status as work progresses
Adds work notes documenting actions taken
Contacts user if additional information needed
Marks ticket as resolved when complete
Moves to next assigned ticket
Admin Workflow
Admin logs in with admin credentials
Accesses system-wide dashboard and analytics
Reviews technician performance metrics
Manages user accounts and permissions
Configures system settings and workflows
Monitors ticket volume and resolution times
Generates reports on support operations
Technical Implementation Details
Database Structure
Snowflake Tables:
TEST_DB.PUBLIC.COMPANY_4130_DATA: Main ticket data
tickets: Newer ticket table with expanded fields
technician_dummy_data: Technician information
user_dummy_data: User information
AI Models Used
Snowflake Cortex LLM Models:
mixtral-8x7b: Primary model for classification and resolution
llama3-8b: Used for metadata extraction
Integration Points
Email Integration: IMAP/SMTP for ticket creation and notifications
Snowflake Cortex: For AI operations and data storage
Streamlit: For web interface and dashboards
Data Flow
User input → Streamlit UI → Database Service
Database Service → Intake Agent → AI Processor
AI Processor → Snowflake Cortex LLM → Classification Results
Classification Results → Assignment Agent → Technician Selection
All Results → Notification Agent → Email Notifications
All Data → Knowledge Base → Future Reference
Authentication
Simple username/password authentication
Role-based access control (User, Technician, Admin)
Mock user database in development (real DB in production)
The system demonstrates a sophisticated application of AI in IT support operations, leveraging Snowflake's Cortex LLM capabilities to automate traditionally manual processes like ticket classification, technician assignment, and resolution suggestion.