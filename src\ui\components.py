"""
Shared UI Components for TeamLogic-AutoTask Application
Contains reusable Streamlit components for dashboards.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

def apply_custom_css():
    """Apply custom CSS styling for the application."""
    st.markdown("""
    <style>
    /* Main theme variables */
    :root {
        --primary-color: #4e73df;
        --secondary-color: #858796;
        --success-color: #1cc88a;
        --info-color: #36b9cc;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
    }
    
    /* Custom metric cards */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 0.35rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        border-left: 0.25rem solid var(--primary-color);
        margin-bottom: 1rem;
    }
    
    .metric-card.success {
        border-left-color: var(--success-color);
    }
    
    .metric-card.warning {
        border-left-color: var(--warning-color);
    }
    
    .metric-card.danger {
        border-left-color: var(--danger-color);
    }
    
    .metric-card.info {
        border-left-color: var(--info-color);
    }
    
    /* Status badges */
    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.35rem;
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.05rem;
    }
    
    .status-open {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .status-in-progress {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-resolved {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-closed {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    /* Priority indicators */
    .priority-low { color: #28a745; }
    .priority-medium { color: #ffc107; }
    .priority-high { color: #fd7e14; }
    .priority-critical { color: #dc3545; }
    .priority-urgent { color: #e83e8c; }
    
    /* Ticket cards */
    .ticket-card {
        background: white;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1rem;
        transition: all 0.3s;
    }
    
    .ticket-card:hover {
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    /* Dark theme support */
    .stApp[data-theme="dark"] .metric-card {
        background: #2d3748;
        color: white;
    }
    
    .stApp[data-theme="dark"] .ticket-card {
        background: #2d3748;
        border-color: #4a5568;
        color: white;
    }
    
    /* Custom buttons */
    .stButton > button {
        border-radius: 0.35rem;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .stButton > button:hover {
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }
    
    /* Sidebar styling */
    .css-1d391kg {
        background-color: var(--light-color);
    }
    
    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    </style>
    """, unsafe_allow_html=True)

def create_metric_card(title: str, value: str, delta: Optional[str] = None, 
                      delta_color: str = "normal", card_type: str = "primary") -> None:
    """Create a styled metric card."""
    delta_html = ""
    if delta:
        color_class = {
            "normal": "text-secondary",
            "inverse": "text-danger" if delta.startswith("+") else "text-success",
            "off": "text-muted"
        }.get(delta_color, "text-secondary")
        delta_html = f'<small class="{color_class}">{delta}</small>'
    
    st.markdown(f"""
    <div class="metric-card {card_type}">
        <div class="row no-gutters align-items-center">
            <div class="col mr-2">
                <div class="text-xs font-weight-bold text-{card_type} text-uppercase mb-1">
                    {title}
                </div>
                <div class="h5 mb-0 font-weight-bold text-gray-800">
                    {value}
                </div>
                {delta_html}
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_status_badge(status: str) -> str:
    """Create a colored status badge."""
    status_lower = status.lower().replace(" ", "-")
    return f'<span class="status-badge status-{status_lower}">{status}</span>'

def get_priority_icon(priority: str) -> str:
    """Get priority icon based on priority level."""
    priority_icons = {
        "Low": "🟢",
        "Medium": "🟡", 
        "High": "🟠",
        "Critical": "🔴",
        "Desktop/User Down": "🚨",
        "Urgent": "🚨"
    }
    return priority_icons.get(priority, "⚪")

def format_time_elapsed(created_at: datetime) -> str:
    """Format time elapsed since ticket creation."""
    if isinstance(created_at, str):
        try:
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        except:
            return "Unknown"
    
    now = datetime.now()
    if created_at.tzinfo:
        now = now.replace(tzinfo=created_at.tzinfo)
    
    elapsed = now - created_at
    
    if elapsed.days > 0:
        return f"{elapsed.days} day{'s' if elapsed.days != 1 else ''} ago"
    elif elapsed.seconds > 3600:
        hours = elapsed.seconds // 3600
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    elif elapsed.seconds > 60:
        minutes = elapsed.seconds // 60
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    else:
        return "Just now"

def format_date_display(date_obj: datetime) -> str:
    """Format date for display."""
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.fromisoformat(date_obj.replace('Z', '+00:00'))
        except:
            return date_obj
    
    return date_obj.strftime("%Y-%m-%d %H:%M")

def get_duration_icon(duration: str) -> str:
    """Get icon for duration filter."""
    duration_icons = {
        "Last hour": "🕐",
        "Last 2 hours": "🕑", 
        "Last 6 hours": "🕕",
        "Last 12 hours": "🕘",
        "Today": "📅",
        "Yesterday": "📆",
        "Last 3 days": "📊",
        "Last week": "📈",
        "Last month": "📉",
        "All tickets": "🗂️"
    }
    return duration_icons.get(duration, "📋")

def create_data_table(data: List[Dict], columns: List[str], 
                     searchable: bool = True, sortable: bool = True) -> None:
    """Create an interactive data table."""
    if not data:
        st.info("No data available")
        return
    
    df = pd.DataFrame(data)
    
    if searchable:
        search_term = st.text_input("🔍 Search", placeholder="Type to search...")
        if search_term:
            mask = df.astype(str).apply(lambda x: x.str.contains(search_term, case=False, na=False)).any(axis=1)
            df = df[mask]
    
    if sortable and not df.empty:
        sort_column = st.selectbox("Sort by", options=columns)
        sort_order = st.radio("Order", ["Ascending", "Descending"], horizontal=True)
        ascending = sort_order == "Ascending"
        df = df.sort_values(by=sort_column, ascending=ascending)
    
    st.dataframe(df[columns], use_container_width=True)

def create_chart_container(chart_type: str, data: Dict, title: str) -> None:
    """Create a chart container with consistent styling."""
    st.subheader(title)
    
    if chart_type == "bar":
        fig = px.bar(
            x=list(data.keys()),
            y=list(data.values()),
            title=title,
            color_discrete_sequence=['#4e73df']
        )
    elif chart_type == "pie":
        fig = px.pie(
            values=list(data.values()),
            names=list(data.keys()),
            title=title
        )
    elif chart_type == "line":
        fig = px.line(
            x=list(data.keys()),
            y=list(data.values()),
            title=title,
            color_discrete_sequence=['#4e73df']
        )
    else:
        st.error(f"Unsupported chart type: {chart_type}")
        return
    
    fig.update_layout(
        showlegend=True,
        height=400,
        margin=dict(l=0, r=0, t=30, b=0)
    )
    
    st.plotly_chart(fig, use_container_width=True)

def create_filter_section(filters: Dict[str, Any]) -> Dict[str, Any]:
    """Create a filter section with multiple filter options."""
    st.subheader("🔍 Filters")
    
    applied_filters = {}
    
    with st.expander("Filter Options", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            if "status" in filters:
                selected_status = st.multiselect(
                    "Status",
                    options=filters["status"],
                    default=filters["status"]
                )
                applied_filters["status"] = selected_status
            
            if "priority" in filters:
                selected_priority = st.multiselect(
                    "Priority", 
                    options=filters["priority"],
                    default=filters["priority"]
                )
                applied_filters["priority"] = selected_priority
        
        with col2:
            if "category" in filters:
                selected_category = st.multiselect(
                    "Category",
                    options=filters["category"], 
                    default=filters["category"]
                )
                applied_filters["category"] = selected_category
            
            if "date_range" in filters:
                date_range = st.date_input(
                    "Date Range",
                    value=(datetime.now() - timedelta(days=30), datetime.now()),
                    max_value=datetime.now()
                )
                applied_filters["date_range"] = date_range
    
    return applied_filters

def api_call(endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict:
    """Make API call to backend (placeholder for now)."""
    # This is a placeholder - in real implementation, this would make HTTP requests
    st.info(f"API Call: {method} {endpoint}")
    if data:
        st.json(data)
    return {"status": "success", "message": "API call simulated"}

def display_api_response(response: Dict) -> None:
    """Display API response in a formatted way."""
    if response.get("status") == "success":
        st.success(response.get("message", "Operation successful"))
    else:
        st.error(response.get("message", "Operation failed"))
    
    if "data" in response:
        st.json(response["data"])

def create_sidebar(current_page: str, user_info: Optional[Dict] = None) -> str:
    """Create a consistent sidebar for navigation."""
    with st.sidebar:
        st.markdown("## 🎫 TeamLogic-AutoTask")
        
        if user_info:
            st.markdown(f"**Welcome, {user_info.get('name', 'User')}!**")
            st.markdown(f"*{user_info.get('email', '')}*")
            st.markdown("---")
        
        # Navigation
        st.markdown("### Navigation")
        
        pages = {
            "🏠 Home": "home",
            "🎫 My Tickets": "tickets", 
            "📊 Dashboard": "dashboard",
            "⚡ Urgent": "urgent",
            "📈 Analytics": "analytics"
        }
        
        selected_page = current_page
        for page_name, page_key in pages.items():
            if st.button(page_name, key=f"nav_{page_key}", use_container_width=True):
                selected_page = page_key
        
        st.markdown("---")
        
        # Theme toggle
        if st.button("🌙 Toggle Theme", key="theme_toggle", use_container_width=True):
            st.session_state.dark_theme = not st.session_state.get("dark_theme", False)
        
        # Support info
        st.markdown("### 📞 Support")
        st.markdown("**Phone:** 9723100860")
        st.markdown("**Email:** <EMAIL>")
        
        return selected_page
