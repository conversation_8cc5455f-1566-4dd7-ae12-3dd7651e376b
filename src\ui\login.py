"""
Login and Authentication System for TeamLogic-AutoTask
Handles user authentication and session management.
"""

import streamlit as st
import sys
import os
from typing import Dict, Optional, <PERSON><PERSON>

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.services.database_service import DatabaseService

class AuthenticationManager:
    """Manages user authentication and session state."""
    
    def __init__(self):
        """Initialize the authentication manager."""
        self.db_service = DatabaseService()
    
    def authenticate_user(self, email: str) -> Optional[Dict]:
        """
        Authenticate user by email (simplified authentication).
        
        Args:
            email (str): User email address
            
        Returns:
            Optional[Dict]: User information if found, None otherwise
        """
        try:
            # Get user from database
            users = self.db_service.get_users()
            
            for user in users:
                if user.get('useremail', '').lower() == email.lower():
                    return {
                        'user_id': user.get('userid'),
                        'name': user.get('name'),
                        'email': user.get('useremail'),
                        'phone': user.get('userphonenumber'),
                        'role': 'user'
                    }
            
            return None
            
        except Exception as e:
            st.error(f"Authentication error: {str(e)}")
            return None
    
    def authenticate_technician(self, technician_id: str) -> Optional[Dict]:
        """
        Authenticate technician by ID.
        
        Args:
            technician_id (str): Technician ID
            
        Returns:
            Optional[Dict]: Technician information if found, None otherwise
        """
        try:
            # Get technician from database
            technicians = self.db_service.get_technicians()
            
            for tech in technicians:
                if tech.get('technician_id', '').upper() == technician_id.upper():
                    return {
                        'technician_id': tech.get('technician_id'),
                        'name': tech.get('name'),
                        'email': tech.get('email'),
                        'role': tech.get('roll', 'technician'),
                        'skills': tech.get('skill', ''),
                        'specialization': tech.get('specialization', ''),
                        'current_workload': tech.get('current_workload', 0)
                    }
            
            return None
            
        except Exception as e:
            st.error(f"Authentication error: {str(e)}")
            return None

def show_user_login() -> Tuple[bool, Optional[Dict]]:
    """
    Show user login form.
    
    Returns:
        Tuple[bool, Optional[Dict]]: (is_authenticated, user_info)
    """
    st.markdown("### 👤 User Login")
    st.markdown("Enter your email address to access your tickets and submit new ones.")
    
    with st.form("user_login_form"):
        email = st.text_input(
            "Email Address",
            placeholder="<EMAIL>",
            help="Enter the email address associated with your account"
        )
        
        submitted = st.form_submit_button("🔑 Login", use_container_width=True)
        
        if submitted:
            if not email:
                st.error("Please enter your email address")
                return False, None
            
            if "@" not in email or "." not in email:
                st.error("Please enter a valid email address")
                return False, None
            
            # Authenticate user
            auth_manager = AuthenticationManager()
            user_info = auth_manager.authenticate_user(email)
            
            if user_info:
                st.success(f"Welcome back, {user_info['name']}!")
                st.session_state.user_info = user_info
                st.session_state.authenticated = True
                st.session_state.user_type = "user"
                st.rerun()
            else:
                st.error("Email not found. Please contact support if you need assistance.")
                return False, None
    
    return False, None

def show_technician_login() -> Tuple[bool, Optional[Dict]]:
    """
    Show technician login form.
    
    Returns:
        Tuple[bool, Optional[Dict]]: (is_authenticated, technician_info)
    """
    st.markdown("### 👨‍🔧 Technician Login")
    st.markdown("Enter your technician ID to access the technician dashboard.")
    
    with st.form("technician_login_form"):
        technician_id = st.text_input(
            "Technician ID",
            placeholder="TECH-001",
            help="Enter your assigned technician ID"
        )
        
        submitted = st.form_submit_button("🔑 Login", use_container_width=True)
        
        if submitted:
            if not technician_id:
                st.error("Please enter your technician ID")
                return False, None
            
            # Authenticate technician
            auth_manager = AuthenticationManager()
            tech_info = auth_manager.authenticate_technician(technician_id)
            
            if tech_info:
                st.success(f"Welcome back, {tech_info['name']}!")
                st.session_state.user_info = tech_info
                st.session_state.authenticated = True
                st.session_state.user_type = "technician"
                st.rerun()
            else:
                st.error("Technician ID not found. Please check your ID or contact support.")
                return False, None
    
    return False, None

def show_login_page(user_type: str) -> Tuple[bool, Optional[Dict]]:
    """
    Show the appropriate login page based on user type.
    
    Args:
        user_type (str): Either "user" or "technician"
        
    Returns:
        Tuple[bool, Optional[Dict]]: (is_authenticated, user_info)
    """
    # Apply custom styling
    st.markdown("""
    <style>
    .login-container {
        max-width: 500px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .login-header h1 {
        color: #4e73df;
        margin-bottom: 0.5rem;
    }
    
    .login-header p {
        color: #858796;
        margin: 0;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Header
    st.markdown("""
    <div class="login-header">
        <h1>🎫 TeamLogic-AutoTask</h1>
        <p>IT Support Ticket Management System</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Show appropriate login form
    if user_type == "user":
        return show_user_login()
    elif user_type == "technician":
        return show_technician_login()
    else:
        st.error("Invalid user type specified")
        return False, None

def logout():
    """Log out the current user."""
    # Clear session state
    for key in ['user_info', 'authenticated', 'user_type']:
        if key in st.session_state:
            del st.session_state[key]
    
    st.success("You have been logged out successfully.")
    st.rerun()

def require_authentication(user_type: str = None) -> Optional[Dict]:
    """
    Require authentication for a page.
    
    Args:
        user_type (str, optional): Required user type ("user" or "technician")
        
    Returns:
        Optional[Dict]: User info if authenticated, None otherwise
    """
    # Check if user is authenticated
    if not st.session_state.get('authenticated', False):
        return None
    
    # Check user type if specified
    if user_type and st.session_state.get('user_type') != user_type:
        st.error(f"Access denied. This page requires {user_type} privileges.")
        return None
    
    return st.session_state.get('user_info')

def get_current_user() -> Optional[Dict]:
    """Get the currently authenticated user."""
    if st.session_state.get('authenticated', False):
        return st.session_state.get('user_info')
    return None

def is_authenticated() -> bool:
    """Check if a user is currently authenticated."""
    return st.session_state.get('authenticated', False)

def get_user_type() -> Optional[str]:
    """Get the type of the currently authenticated user."""
    return st.session_state.get('user_type')
