"""
Technician Dashboard for TeamLogic-AutoTask Application
Comprehensive technician interface for ticket management, status updates, and analytics.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import sys
import os

# Add the src directory to the path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.ui.components import (
    apply_custom_css, create_metric_card, create_status_badge, 
    get_priority_icon, format_time_elapsed, format_date_display,
    create_chart_container, create_filter_section, create_sidebar
)
from src.ui.login import show_login_page, require_authentication, logout
from src.services.database_service import DatabaseService
from config import PRIORITY_OPTIONS, STATUS_OPTIONS

class TechnicianDashboard:
    """Main Technician Dashboard class."""
    
    def __init__(self):
        """Initialize the technician dashboard."""
        self.db_service = DatabaseService()
        
        # Initialize session state
        if "current_page" not in st.session_state:
            st.session_state.current_page = "home"
        if "dark_theme" not in st.session_state:
            st.session_state.dark_theme = False
        if "show_work_note_modal" not in st.session_state:
            st.session_state.show_work_note_modal = False
        if "selected_ticket_id" not in st.session_state:
            st.session_state.selected_ticket_id = None
    
    def show_header(self, page_title: str, tech_info: Dict):
        """Show the page header with technician information."""
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.title(f"🔧 {page_title}")
            st.markdown(f"**Welcome, {tech_info['name']}!** ({tech_info['technician_id']})")
            st.markdown(f"*{tech_info.get('role', 'Technician')} - {tech_info.get('specialization', 'General')}*")
        
        with col2:
            st.markdown("### Today's Stats")
            # Get technician's ticket stats
            tickets = self.get_technician_tickets(tech_info['technician_id'])
            today_tickets = self.get_today_tickets(tickets)
            
            assigned_today = len([t for t in today_tickets if t.get('status') == 'Assigned'])
            in_progress_today = len([t for t in today_tickets if t.get('status') == 'In Progress'])
            completed_today = len([t for t in today_tickets if t.get('status') in ['Resolved', 'Closed']])
            
            st.metric("Assigned Today", assigned_today)
            st.metric("In Progress", in_progress_today)
            st.metric("Completed Today", completed_today)
        
        with col3:
            st.markdown("### Quick Actions")
            if st.button("🔄 Refresh Data", key="header_refresh"):
                st.rerun()
            if st.button("📝 Add Work Note", key="header_add_note"):
                st.session_state.show_work_note_modal = True
                st.rerun()
            if st.button("🚪 Logout", key="header_logout"):
                logout()
    
    def get_technician_tickets(self, technician_id: str) -> List[Dict]:
        """Get tickets assigned to the technician."""
        try:
            all_tickets = self.db_service.get_tickets()
            tech_tickets = [
                ticket for ticket in all_tickets 
                if ticket.get('assigned_technician', '').upper() == technician_id.upper()
            ]
            return tech_tickets
        except Exception as e:
            st.error(f"Error fetching tickets: {str(e)}")
            return []
    
    def get_today_tickets(self, tickets: List[Dict]) -> List[Dict]:
        """Filter tickets for today."""
        today = datetime.now().date()
        today_tickets = []
        
        for ticket in tickets:
            created_date = ticket.get('created_at', '')
            if isinstance(created_date, str):
                try:
                    ticket_date = datetime.fromisoformat(created_date.replace('Z', '+00:00')).date()
                    if ticket_date == today:
                        today_tickets.append(ticket)
                except:
                    continue
        
        return today_tickets
    
    def update_ticket_status(self, ticket_id: str, new_status: str) -> bool:
        """Update ticket status in the database."""
        try:
            # Update ticket status in database
            update_query = """
            UPDATE tickets
            SET status = %s, updated_at = %s
            WHERE ticket_id = %s
            """

            current_time = datetime.now().isoformat()

            if self.db_service.db_connection:
                self.db_service.db_connection.execute_query(
                    update_query,
                    (new_status, current_time, ticket_id)
                )

                # If resolving ticket, trigger notification
                if new_status == 'Resolved':
                    self._send_resolution_notification(ticket_id)

                st.success(f"✅ Ticket {ticket_id} status updated to {new_status}")
                return True
            else:
                st.error("Database connection not available")
                return False

        except Exception as e:
            st.error(f"❌ Error updating ticket status: {str(e)}")
            return False

    def update_ticket_priority(self, ticket_id: str, new_priority: str) -> bool:
        """Update ticket priority in the database."""
        try:
            # Update ticket priority in database
            update_query = """
            UPDATE tickets
            SET priority = %s, updated_at = %s
            WHERE ticket_id = %s
            """

            current_time = datetime.now().isoformat()

            if self.db_service.db_connection:
                self.db_service.db_connection.execute_query(
                    update_query,
                    (new_priority, current_time, ticket_id)
                )
                st.success(f"✅ Ticket {ticket_id} priority updated to {new_priority}")
                return True
            else:
                st.error("Database connection not available")
                return False

        except Exception as e:
            st.error(f"❌ Error updating ticket priority: {str(e)}")
            return False

    def add_work_note(self, ticket_id: str, note: str) -> bool:
        """Add a work note to a ticket."""
        try:
            # Add work note to database (this would typically be a separate table)
            # For now, we'll update the ticket with the note
            current_time = datetime.now().isoformat()
            technician_id = st.session_state.get('user_info', {}).get('technician_id', 'Unknown')

            # Format the work note with timestamp and technician
            formatted_note = f"[{current_time}] {technician_id}: {note}"

            # Update ticket with work note (in a real system, this would be a separate work_notes table)
            update_query = """
            UPDATE tickets
            SET work_notes = COALESCE(work_notes, '') || %s || '\n',
                updated_at = %s
            WHERE ticket_id = %s
            """

            if self.db_service.db_connection:
                self.db_service.db_connection.execute_query(
                    update_query,
                    (formatted_note, current_time, ticket_id)
                )
                st.success(f"✅ Work note added to ticket {ticket_id}")
                return True
            else:
                st.error("Database connection not available")
                return False

        except Exception as e:
            st.error(f"❌ Error adding work note: {str(e)}")
            return False

    def _send_resolution_notification(self, ticket_id: str):
        """Send notification when ticket is resolved."""
        try:
            # Get ticket details for notification
            tickets = self.db_service.get_tickets()
            ticket = next((t for t in tickets if t.get('ticket_id') == ticket_id), None)

            if ticket and self.db_service.intake_agent:
                # Use the notification agent to send email
                notification_agent = self.db_service.intake_agent.notification_agent
                if notification_agent:
                    notification_agent.send_resolution_notification(
                        ticket.get('requester_email', ''),
                        ticket_id,
                        ticket.get('title', ''),
                        ticket.get('resolution_notes', 'Issue has been resolved.')
                    )
                    st.info("📧 Resolution notification sent to user")

        except Exception as e:
            st.warning(f"Could not send notification: {str(e)}")

    def _resolve_ticket_with_notes(self, ticket_id: str, resolution_notes: str) -> bool:
        """Resolve ticket with resolution notes."""
        try:
            current_time = datetime.now().isoformat()
            technician_id = st.session_state.get('user_info', {}).get('technician_id', 'Unknown')

            # Update ticket status and add resolution notes
            update_query = """
            UPDATE tickets
            SET status = 'Resolved',
                resolution_notes = %s,
                resolved_by = %s,
                resolved_at = %s,
                updated_at = %s
            WHERE ticket_id = %s
            """

            if self.db_service.db_connection:
                self.db_service.db_connection.execute_query(
                    update_query,
                    (resolution_notes, technician_id, current_time, current_time, ticket_id)
                )

                # Send resolution notification
                self._send_resolution_notification(ticket_id)

                st.success(f"✅ Ticket {ticket_id} marked as resolved")
                return True
            else:
                st.error("Database connection not available")
                return False

        except Exception as e:
            st.error(f"❌ Error resolving ticket: {str(e)}")
            return False
    
    def show_ticket_card(self, ticket: Dict, show_actions: bool = True):
        """Display a ticket card with actions."""
        priority_icon = get_priority_icon(ticket.get('priority', 'Medium'))
        status = ticket.get('status', 'Unknown')
        
        # Determine card styling based on priority
        card_class = "ticket-card"
        if ticket.get('priority') in ['Critical', 'Desktop/User Down']:
            card_class += " urgent"
        
        with st.container():
            col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
            
            with col1:
                st.markdown(f"**{priority_icon} {ticket.get('title', 'Untitled')}**")
                st.write(f"ID: {ticket.get('ticket_id', 'N/A')}")
                st.write(f"Requester: {ticket.get('requester_name', 'Unknown')}")
            
            with col2:
                st.write(create_status_badge(status), unsafe_allow_html=True)
                st.write(f"Priority: {ticket.get('priority', 'Medium')}")
            
            with col3:
                st.write(f"Created: {format_time_elapsed(ticket.get('created_at', ''))}")
                if ticket.get('due_date'):
                    st.write(f"Due: {format_date_display(ticket.get('due_date', ''))}")
            
            with col4:
                if show_actions:
                    self.show_ticket_actions(ticket)
            
            # Expandable details
            with st.expander("📋 View Details"):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write(f"**Description:** {ticket.get('description', 'No description')}")
                    if ticket.get('device_model'):
                        st.write(f"**Device:** {ticket.get('device_model')}")
                    if ticket.get('os_version'):
                        st.write(f"**OS:** {ticket.get('os_version')}")
                    if ticket.get('error_message'):
                        st.write(f"**Error:** {ticket.get('error_message')}")
                
                with col2:
                    st.write(f"**Contact:** {ticket.get('requester_email', 'N/A')}")
                    if ticket.get('requester_phone'):
                        st.write(f"**Phone:** {ticket.get('requester_phone')}")
                    st.write(f"**Location:** {ticket.get('location', 'Not specified')}")

                    # Show AI classification info if available
                    if ticket.get('category'):
                        st.write(f"**AI Category:** {ticket.get('category')}")
                    if ticket.get('sub_category'):
                        st.write(f"**Sub-Category:** {ticket.get('sub_category')}")

                    # Show workflow status
                    st.markdown("**🤖 AI Workflow:**")
                    workflow_status = "✅ Classified → ✅ Assigned"
                    if ticket.get('status') == 'In Progress':
                        workflow_status += " → 🔄 In Progress"
                    elif ticket.get('status') == 'Resolved':
                        workflow_status += " → ✅ Resolved"
                    st.markdown(f"<small>{workflow_status}</small>", unsafe_allow_html=True)

                    # Show work notes if available
                    if ticket.get('work_notes'):
                        with st.expander("📝 Work Notes"):
                            st.text(ticket.get('work_notes'))

                    if ticket.get('resolution_notes'):
                        st.success(f"**Resolution:** {ticket.get('resolution_notes')}")
            
            st.markdown("---")
    
    def show_ticket_actions(self, ticket: Dict):
        """Show action buttons for a ticket."""
        ticket_id = ticket.get('ticket_id', '')
        current_status = ticket.get('status', 'Unknown')
        
        # Status-specific actions
        if current_status == 'Assigned':
            if st.button("▶️ Start Work", key=f"start_{ticket_id}"):
                if self.update_ticket_status(ticket_id, 'In Progress'):
                    st.rerun()
        
        elif current_status == 'In Progress':
            if st.button("✅ Mark Resolved", key=f"resolve_{ticket_id}"):
                # Show resolution form
                st.session_state[f"show_resolution_{ticket_id}"] = True
                st.rerun()

        # Show resolution form if needed
        if st.session_state.get(f"show_resolution_{ticket_id}", False):
            with st.form(f"resolution_form_{ticket_id}"):
                st.subheader("📝 Resolution Details")
                resolution_note = st.text_area(
                    "Resolution Notes",
                    placeholder="Describe how the issue was resolved...",
                    height=100
                )

                col1, col2 = st.columns(2)
                with col1:
                    if st.form_submit_button("✅ Confirm Resolution"):
                        if resolution_note:
                            # Update ticket with resolution
                            if self._resolve_ticket_with_notes(ticket_id, resolution_note):
                                st.session_state[f"show_resolution_{ticket_id}"] = False
                                st.rerun()
                        else:
                            st.error("Please provide resolution notes")

                with col2:
                    if st.form_submit_button("❌ Cancel"):
                        st.session_state[f"show_resolution_{ticket_id}"] = False
                        st.rerun()
        
        # Universal actions
        if st.button("📝 Add Note", key=f"note_{ticket_id}"):
            st.session_state.selected_ticket_id = ticket_id
            st.session_state.show_work_note_modal = True
            st.rerun()
        
        # Contact actions
        col1, col2 = st.columns(2)
        with col1:
            if st.button("📞 Call", key=f"call_{ticket_id}"):
                st.info(f"Calling {ticket.get('requester_phone', 'No phone number')}")
        
        with col2:
            if st.button("✉️ Email", key=f"email_{ticket_id}"):
                st.info(f"Opening email to {ticket.get('requester_email', 'No email')}")
    
    def show_work_note_modal(self):
        """Show the work note modal."""
        if st.session_state.show_work_note_modal:
            with st.form("work_note_form"):
                st.subheader("📝 Add Work Note")
                
                ticket_id = st.session_state.selected_ticket_id or st.text_input("Ticket ID")
                note_text = st.text_area("Work Note", height=100, 
                                       placeholder="Describe the work performed, findings, or next steps...")
                
                col1, col2 = st.columns(2)
                with col1:
                    if st.form_submit_button("💾 Save Note"):
                        if ticket_id and note_text:
                            if self.add_work_note(ticket_id, note_text):
                                st.session_state.show_work_note_modal = False
                                st.session_state.selected_ticket_id = None
                                st.rerun()
                        else:
                            st.error("Please fill in all fields")
                
                with col2:
                    if st.form_submit_button("❌ Cancel"):
                        st.session_state.show_work_note_modal = False
                        st.session_state.selected_ticket_id = None
                        st.rerun()
    
    def show_main_page(self, tech_info: Dict):
        """Show the main technician dashboard."""
        self.show_header("Technician Dashboard", tech_info)
        
        # Show work note modal if needed
        self.show_work_note_modal()
        
        st.markdown("---")
        
        tickets = self.get_technician_tickets(tech_info['technician_id'])
        
        if not tickets:
            st.info("No tickets assigned to you at the moment.")
            return
        
        # Categorize tickets
        urgent_tickets = [t for t in tickets if t.get('priority') in ['Critical', 'Desktop/User Down']]
        assigned_tickets = [t for t in tickets if t.get('status') == 'Assigned']
        in_progress_tickets = [t for t in tickets if t.get('status') == 'In Progress']
        
        # Show urgent tickets first
        if urgent_tickets:
            st.subheader("🚨 Urgent Tickets")
            st.error("These tickets require immediate attention!")
            
            for ticket in urgent_tickets:
                self.show_ticket_card(ticket)
        
        # Show assigned tickets
        if assigned_tickets:
            st.subheader("📋 Assigned Tickets")
            
            for ticket in assigned_tickets:
                self.show_ticket_card(ticket)
        
        # Show in-progress tickets
        if in_progress_tickets:
            st.subheader("⚡ In Progress Tickets")
            
            for ticket in in_progress_tickets:
                self.show_ticket_card(ticket)

    def show_all_tickets_page(self, tech_info: Dict):
        """Show all tickets assigned to the technician."""
        self.show_header("All My Tickets", tech_info)

        # Show work note modal if needed
        self.show_work_note_modal()

        st.markdown("---")

        tickets = self.get_technician_tickets(tech_info['technician_id'])

        if not tickets:
            st.info("No tickets assigned to you.")
            return

        # Filters
        filters = {
            "status": list(set([t.get('status', 'Unknown') for t in tickets])),
            "priority": list(set([t.get('priority', 'Medium') for t in tickets])),
            "date_range": True
        }

        applied_filters = create_filter_section(filters)

        # Filter tickets
        filtered_tickets = tickets

        if applied_filters.get('status'):
            filtered_tickets = [t for t in filtered_tickets if t.get('status') in applied_filters['status']]

        if applied_filters.get('priority'):
            filtered_tickets = [t for t in filtered_tickets if t.get('priority') in applied_filters['priority']]

        # Sort options
        sort_options = ["Created Date", "Priority", "Status", "Due Date"]
        sort_by = st.selectbox("Sort by", sort_options)
        sort_order = st.radio("Order", ["Newest First", "Oldest First"], horizontal=True)

        # Sort tickets
        if sort_by == "Created Date":
            filtered_tickets.sort(key=lambda x: x.get('created_at', ''), reverse=(sort_order == "Newest First"))
        elif sort_by == "Priority":
            priority_order = {"Critical": 4, "Desktop/User Down": 4, "High": 3, "Medium": 2, "Low": 1}
            filtered_tickets.sort(key=lambda x: priority_order.get(x.get('priority', 'Medium'), 2), reverse=True)
        elif sort_by == "Status":
            filtered_tickets.sort(key=lambda x: x.get('status', 'Unknown'))

        # Display tickets
        st.subheader(f"📋 Tickets ({len(filtered_tickets)} found)")

        for ticket in filtered_tickets:
            self.show_ticket_card(ticket)

    def show_urgent_tickets_page(self, tech_info: Dict):
        """Show urgent tickets requiring immediate attention."""
        self.show_header("Urgent Tickets", tech_info)

        # Show work note modal if needed
        self.show_work_note_modal()

        st.markdown("---")

        tickets = self.get_technician_tickets(tech_info['technician_id'])
        urgent_tickets = [t for t in tickets if t.get('priority') in ['Critical', 'Desktop/User Down']]

        if not urgent_tickets:
            st.success("🎉 No urgent tickets at the moment!")
            return

        st.error(f"⚠️ {len(urgent_tickets)} urgent ticket(s) require immediate attention!")

        # Sort by creation time (newest first)
        urgent_tickets.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        for ticket in urgent_tickets:
            # Add special styling for urgent tickets
            with st.container():
                st.markdown("""
                <div style="border: 2px solid #dc3545; border-radius: 5px; padding: 10px; margin: 10px 0; background-color: #f8d7da;">
                """, unsafe_allow_html=True)

                self.show_ticket_card(ticket)

                # Add "Start Immediately" button for assigned urgent tickets
                if ticket.get('status') == 'Assigned':
                    if st.button(f"🚨 Start Immediately", key=f"urgent_start_{ticket.get('ticket_id')}"):
                        if self.update_ticket_status(ticket.get('ticket_id'), 'In Progress'):
                            st.rerun()

                st.markdown("</div>", unsafe_allow_html=True)

    def show_analytics_page(self, tech_info: Dict):
        """Show analytics and performance metrics."""
        self.show_header("Analytics Dashboard", tech_info)

        st.markdown("---")

        tickets = self.get_technician_tickets(tech_info['technician_id'])

        if not tickets:
            st.info("No tickets found for analytics.")
            return

        # Time period selector
        time_periods = ["Last 7 days", "Last 30 days", "Last 90 days", "All time"]
        selected_period = st.selectbox("Time Period", time_periods, index=1)

        # Calculate metrics
        total_tickets = len(tickets)
        resolved_tickets = len([t for t in tickets if t.get('status') == 'Resolved'])
        in_progress_tickets = len([t for t in tickets if t.get('status') == 'In Progress'])
        resolution_rate = (resolved_tickets / total_tickets * 100) if total_tickets > 0 else 0

        # Metrics row
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            create_metric_card("Total Tickets", str(total_tickets), card_type="primary")

        with col2:
            create_metric_card("Resolved", str(resolved_tickets), card_type="success")

        with col3:
            create_metric_card("Resolution Rate", f"{resolution_rate:.1f}%", card_type="info")

        with col4:
            avg_resolution_time = "2.3 days"  # This would be calculated from actual data
            create_metric_card("Avg Resolution Time", avg_resolution_time, card_type="warning")

        # Charts row
        col1, col2 = st.columns(2)

        with col1:
            # Status distribution pie chart
            status_counts = {}
            for ticket in tickets:
                status = ticket.get('status', 'Unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            if status_counts:
                fig = px.pie(
                    values=list(status_counts.values()),
                    names=list(status_counts.keys()),
                    title="Tickets by Status"
                )
                st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Priority distribution bar chart
            priority_counts = {}
            for ticket in tickets:
                priority = ticket.get('priority', 'Medium')
                priority_counts[priority] = priority_counts.get(priority, 0) + 1

            if priority_counts:
                fig = px.bar(
                    x=list(priority_counts.keys()),
                    y=list(priority_counts.values()),
                    title="Tickets by Priority",
                    color_discrete_sequence=['#4e73df']
                )
                st.plotly_chart(fig, use_container_width=True)

        # Daily trends (mock data for now)
        st.subheader("📈 Daily Ticket Trends")

        # This would use real data in production
        dates = pd.date_range(start='2024-01-01', periods=30, freq='D')
        ticket_counts = [2, 3, 1, 4, 2, 5, 3, 2, 4, 1, 3, 2, 4, 5, 2, 3, 1, 4, 2, 3, 4, 2, 1, 3, 2, 4, 3, 2, 1, 3]

        fig = px.line(
            x=dates,
            y=ticket_counts,
            title="Daily Ticket Volume",
            labels={'x': 'Date', 'y': 'Number of Tickets'}
        )
        st.plotly_chart(fig, use_container_width=True)

def main():
    """Main function for the Technician Dashboard."""
    # Configure page
    st.set_page_config(
        page_title="Technician Dashboard - TeamLogic-AutoTask",
        layout="wide",
        page_icon="🔧",
        initial_sidebar_state="expanded"
    )
    
    # Apply custom CSS
    apply_custom_css()
    
    # Check authentication
    tech_info = require_authentication("technician")
    
    if not tech_info:
        # Show login page
        show_login_page("technician")
        return
    
    # Initialize dashboard
    dashboard = TechnicianDashboard()
    
    # Sidebar navigation
    current_page = create_sidebar(st.session_state.current_page, tech_info)
    st.session_state.current_page = current_page
    
    # Show appropriate page
    if current_page == "home":
        dashboard.show_main_page(tech_info)
    elif current_page == "tickets":
        dashboard.show_all_tickets_page(tech_info)
    elif current_page == "urgent":
        dashboard.show_urgent_tickets_page(tech_info)
    elif current_page == "analytics":
        dashboard.show_analytics_page(tech_info)
    else:
        dashboard.show_main_page(tech_info)

if __name__ == "__main__":
    main()
